# 信息补推时间逻辑修复

## 问题描述

信息补推功能的时间逻辑计算存在问题，原有实现只是简单地使用当前时间减去预警间隔来计算查询的起始时间，没有正确考虑接收时间（DAILY/WORKDAYS/HOLIDAYS）和接收时段两个变量，特别是在接收时段后、周末后、节假日后的情况。

## 问题分析

### 原有逻辑问题
```java
// 原有的错误实现
LocalDateTime intervalStart = now.minusMinutes(receptionSettings.alertInterval());
```

这种简单的时间计算方式无法正确处理以下场景：
1. **接收时段后**：如果接收时段是09:00-18:00，当前时间是20:00，应该查找从18:00到20:00的预警
2. **周末后**：如果接收时间是WORKDAYS，当前时间是周六，应该查找从上周五18:00到现在的预警
3. **节假日后**：如果接收时间是HOLIDAYS，当前时间是周一，应该查找从上周日18:00到现在的预警
4. **跨天接收时段**：如果接收时段是22:00-06:00，需要正确处理跨天的情况

### 正确的逻辑
信息补推应该查找的时间范围是：**从上一个有效接收时段的结束时间到当前时间**

## 解决方案

### 1. 新增接口方法
在`ReceptionRulesEngine`接口中添加新方法：
```java
/**
 * Calculate the end time of the last valid reception period
 * Used for info push notifications to determine the correct time range
 * Considers reception time (DAILY/WORKDAYS/HOLIDAYS) and reception period
 * 
 * @param settings    Reception settings containing reception time and period
 * @param currentTime Current time to calculate from
 * @return End time of the last valid reception period
 */
LocalDateTime calculateLastReceptionPeriodEnd(ReceptionSettingsDto settings, LocalDateTime currentTime);
```

### 2. 实现时间计算逻辑
在`ReceptionRulesEngineImpl`中实现该方法：

**核心算法**：
1. 如果没有设置接收时段，使用默认间隔
2. 从当前时间开始，向前查找最近的有效接收日期
3. 在该日期上应用接收时段，计算结束时间
4. 处理跨天接收时段的特殊情况
5. 最多向前查找7天，避免无限循环

**关键处理**：
- **24:00时间处理**：将"24:00"转换为23:59:59
- **跨天时段**：正确计算跨天接收时段的结束时间
- **接收时间类型**：支持DAILY、WORKDAYS、HOLIDAYS的判断
- **异常处理**：解析失败时使用默认间隔作为fallback

### 3. 修改信息补推调度逻辑
在`AlertNotificationSchedulerImpl.scheduleInfoPushNotifications`方法中：
```java
// 修改前
LocalDateTime intervalStart = now.minusMinutes(receptionSettings.alertInterval());

// 修改后
LocalDateTime intervalStart = receptionRulesEngine.calculateLastReceptionPeriodEnd(receptionSettings, now);
```

## 测试覆盖

添加了完整的单元测试覆盖以下场景：
1. **无接收时段**：返回默认间隔时间
2. **同一天接收时段**：09:00-18:00，当前20:00，返回18:00
3. **跨天接收时段**：22:00-06:00，当前20:00，返回当天06:00
4. **工作日限制**：WORKDAYS，当前周六，返回上周五18:00
5. **节假日限制**：HOLIDAYS，当前周一，返回上周日18:00
6. **24:00时间处理**：正确处理24:00结束时间

## 验证结果

- ✅ 所有新增测试通过
- ✅ 现有测试套件全部通过（300个测试）
- ✅ 编译无错误和警告
- ✅ 代码符合项目规范

## 影响范围

**修改的文件**：
- `ReceptionRulesEngine.java` - 添加新接口方法
- `ReceptionRulesEngineImpl.java` - 实现时间计算逻辑
- `AlertNotificationSchedulerImpl.java` - 修改信息补推调度逻辑
- `ReceptionRulesEngineImplTest.java` - 添加单元测试

**向后兼容性**：
- ✅ 完全向后兼容，不影响现有功能
- ✅ 只修改信息补推的时间计算逻辑
- ✅ 其他通知类型（预警通知、无预警通知）不受影响

## 总结

此次修复解决了信息补推时间逻辑的核心问题，使其能够正确处理复杂的接收规则配置，特别是在跨时段、跨天、跨周末和节假日的场景下。修复后的逻辑更加准确和可靠，符合业务需求。
