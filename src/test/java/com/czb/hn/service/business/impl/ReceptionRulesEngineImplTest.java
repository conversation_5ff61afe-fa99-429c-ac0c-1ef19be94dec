package com.czb.hn.service.business.impl;

import com.czb.hn.dto.alert.config.ReceptionSettingsDto;
import com.czb.hn.jpa.securadar.entity.AlertResult;
import com.czb.hn.enums.NotificationType;
import com.czb.hn.service.business.ReceptionRulesEngine;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for ReceptionRulesEngineImpl
 */
@ExtendWith(MockitoExtension.class)
class ReceptionRulesEngineImplTest {

    private ReceptionRulesEngineImpl receptionRulesEngine;
    private ReceptionSettingsDto defaultSettings;
    private AlertResult testAlert;

    @BeforeEach
    void setUp() {
        receptionRulesEngine = new ReceptionRulesEngineImpl();

        // Create default reception settings
        ReceptionSettingsDto.TimePeriodDto timePeriod = new ReceptionSettingsDto.TimePeriodDto("09:00", "18:00");

        ReceptionSettingsDto.EmailRecipientDto emailRecipient = new ReceptionSettingsDto.EmailRecipientDto("Admin",
                "<EMAIL>");
        ReceptionSettingsDto.EmailConfigDto emailConfig = new ReceptionSettingsDto.EmailConfigDto(true,
                List.of(emailRecipient));

        ReceptionSettingsDto.SmsRecipientDto smsRecipient = new ReceptionSettingsDto.SmsRecipientDto("Admin",
                "13800138000");
        ReceptionSettingsDto.SmsConfigDto smsConfig = new ReceptionSettingsDto.SmsConfigDto(true,
                List.of(smsRecipient));

        ReceptionSettingsDto.ReceptionMethodsDto receptionMethods = new ReceptionSettingsDto.ReceptionMethodsDto(
                emailConfig, smsConfig);

        defaultSettings = new ReceptionSettingsDto(
                "DAILY", 30, timePeriod, false, receptionMethods, false);

        testAlert = AlertResult.builder()
                .id(1L)
                .enterpriseId("enterprise123")
                .title("测试预警")
                .content("测试预警内容")
                .warningTime(LocalDateTime.now())
                .build();
    }

    @Test
    void testIsWithinReceptionPeriod_WithinPeriod() {
        // Given
        LocalDateTime currentTime = LocalDateTime.of(2024, 1, 15, 10, 0); // 10:00

        // When
        boolean result = receptionRulesEngine.isWithinReceptionPeriod(defaultSettings, currentTime);

        // Then
        assertTrue(result);
    }

    @Test
    void testIsWithinReceptionPeriod_OutsidePeriod() {
        // Given
        LocalDateTime currentTime = LocalDateTime.of(2024, 1, 15, 20, 0); // 20:00

        // When
        boolean result = receptionRulesEngine.isWithinReceptionPeriod(defaultSettings, currentTime);

        // Then
        assertFalse(result);
    }

    @Test
    void testIsWithinReceptionPeriod_OvernightPeriod() {
        // Given
        ReceptionSettingsDto.TimePeriodDto overnightPeriod = new ReceptionSettingsDto.TimePeriodDto("22:00", "06:00");
        ReceptionSettingsDto.ReceptionMethodsDto methods = defaultSettings.receptionMethods();
        ReceptionSettingsDto settingsWithOvernightPeriod = new ReceptionSettingsDto(
                "DAILY", 30, overnightPeriod, false, methods, false);

        LocalDateTime currentTime = LocalDateTime.of(2024, 1, 15, 23, 0); // 23:00

        // When
        boolean result = receptionRulesEngine.isWithinReceptionPeriod(settingsWithOvernightPeriod, currentTime);

        // Then
        assertTrue(result);
    }

    @Test
    void testMatchesReceptionSchedule_Daily() {
        // Given
        LocalDateTime mondayTime = LocalDateTime.of(2024, 1, 15, 10, 0); // Monday
        LocalDateTime sundayTime = LocalDateTime.of(2024, 1, 14, 10, 0); // Sunday

        // When & Then
        assertTrue(receptionRulesEngine.matchesReceptionSchedule(defaultSettings, mondayTime));
        assertTrue(receptionRulesEngine.matchesReceptionSchedule(defaultSettings, sundayTime));
    }

    @Test
    void testMatchesReceptionSchedule_Workdays() {
        // Given
        ReceptionSettingsDto.ReceptionMethodsDto methods = defaultSettings.receptionMethods();
        ReceptionSettingsDto workdaySettings = new ReceptionSettingsDto(
                "WORKDAYS", 30, defaultSettings.receptionPeriod(), false, methods, false);

        LocalDateTime mondayTime = LocalDateTime.of(2024, 1, 15, 10, 0); // Monday
        LocalDateTime sundayTime = LocalDateTime.of(2024, 1, 14, 10, 0); // Sunday

        // When & Then
        assertTrue(receptionRulesEngine.matchesReceptionSchedule(workdaySettings, mondayTime));
        assertFalse(receptionRulesEngine.matchesReceptionSchedule(workdaySettings, sundayTime));
    }

    @Test
    void testMatchesReceptionSchedule_Holidays() {
        // Given
        ReceptionSettingsDto.ReceptionMethodsDto methods = defaultSettings.receptionMethods();
        ReceptionSettingsDto holidaySettings = new ReceptionSettingsDto(
                "HOLIDAYS", 30, defaultSettings.receptionPeriod(), false, methods, false);

        LocalDateTime mondayTime = LocalDateTime.of(2024, 1, 15, 10, 0); // Monday
        LocalDateTime sundayTime = LocalDateTime.of(2024, 1, 14, 10, 0); // Sunday

        // When & Then
        assertFalse(receptionRulesEngine.matchesReceptionSchedule(holidaySettings, mondayTime));
        assertTrue(receptionRulesEngine.matchesReceptionSchedule(holidaySettings, sundayTime));
    }

    @Test
    void testExtractRecipients() {
        // When
        List<ReceptionRulesEngine.RecipientInfo> recipients = receptionRulesEngine.extractRecipients(defaultSettings);

        // Then
        assertEquals(2, recipients.size());

        // Check email recipient
        ReceptionRulesEngine.RecipientInfo emailRecipient = recipients.stream()
                .filter(r -> r.emailEnabled())
                .findFirst()
                .orElse(null);
        assertNotNull(emailRecipient);
        assertEquals("Admin", emailRecipient.name());
        assertEquals("<EMAIL>", emailRecipient.email());
        assertTrue(emailRecipient.emailEnabled());
        assertFalse(emailRecipient.smsEnabled());

        // Check SMS recipient
        ReceptionRulesEngine.RecipientInfo smsRecipient = recipients.stream()
                .filter(r -> r.smsEnabled())
                .findFirst()
                .orElse(null);
        assertNotNull(smsRecipient);
        assertEquals("Admin", smsRecipient.name());
        assertEquals("13800138000", smsRecipient.phone());
        assertFalse(smsRecipient.emailEnabled());
        assertTrue(smsRecipient.smsEnabled());
    }

    @Test
    void testHasIntervalPassed_NoLastNotification() {
        // Given
        LocalDateTime currentTime = LocalDateTime.now();

        // When
        boolean result = receptionRulesEngine.hasIntervalPassed(defaultSettings, null, currentTime);

        // Then
        assertTrue(result);
    }

    @Test
    void testHasIntervalPassed_IntervalMet() {
        // Given
        LocalDateTime lastNotificationTime = LocalDateTime.now().minusMinutes(35);
        LocalDateTime currentTime = LocalDateTime.now();

        // When
        boolean result = receptionRulesEngine.hasIntervalPassed(defaultSettings, lastNotificationTime, currentTime);

        // Then
        assertTrue(result);
    }

    @Test
    void testHasIntervalPassed_IntervalNotMet() {
        // Given
        LocalDateTime lastNotificationTime = LocalDateTime.now().minusMinutes(15);
        LocalDateTime currentTime = LocalDateTime.now();

        // When
        boolean result = receptionRulesEngine.hasIntervalPassed(defaultSettings, lastNotificationTime, currentTime);

        // Then
        assertFalse(result);
    }

    @Test
    void testDetermineNotificationType_AlertWithinPeriod() {
        // When
        NotificationType result = receptionRulesEngine.determineNotificationType(testAlert, defaultSettings, true);

        // Then
        assertEquals(NotificationType.ALERT, result);
    }

    @Test
    void testDetermineNotificationType_AlertOutsidePeriodWithInfoPush() {
        // Given
        ReceptionSettingsDto.ReceptionMethodsDto methods = defaultSettings.receptionMethods();
        ReceptionSettingsDto settingsWithInfoPush = new ReceptionSettingsDto(
                "DAILY", 30, defaultSettings.receptionPeriod(), true, methods, false);

        // When
        NotificationType result = receptionRulesEngine.determineNotificationType(testAlert, settingsWithInfoPush,
                false);

        // Then
        assertEquals(NotificationType.INFO_PUSH, result);
    }

    @Test
    void testDetermineNotificationType_NoAlertWithNotificationEnabled() {
        // Given
        ReceptionSettingsDto.ReceptionMethodsDto methods = defaultSettings.receptionMethods();
        ReceptionSettingsDto settingsWithNoAlert = new ReceptionSettingsDto(
                "DAILY", 30, defaultSettings.receptionPeriod(), false, methods, true);

        // When
        NotificationType result = receptionRulesEngine.determineNotificationType(null, settingsWithNoAlert, true);

        // Then
        assertEquals(NotificationType.NO_ALERT, result);
    }

    @Test
    void testValidateReceptionSettings_Valid() {
        // When & Then
        assertDoesNotThrow(() -> receptionRulesEngine.validateReceptionSettings(defaultSettings));
    }

    @Test
    void testValidateReceptionSettings_NullSettings() {
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            receptionRulesEngine.validateReceptionSettings(null);
        });
        assertEquals("Reception settings cannot be null", exception.getMessage());
    }

    @Test
    void testCalculateNextNotificationTime() {
        // Given
        LocalDateTime currentTime = LocalDateTime.of(2024, 1, 15, 10, 0);
        LocalDateTime lastNotificationTime = LocalDateTime.of(2024, 1, 15, 9, 0);

        // When
        LocalDateTime result = receptionRulesEngine.calculateNextNotificationTime(
                defaultSettings, currentTime, lastNotificationTime);

        // Then
        assertNotNull(result);
        assertTrue(result.isAfter(currentTime) || result.equals(currentTime));
    }

    @Test
    void testEvaluateNotificationSchedule_ShouldSchedule() {
        // Given
        LocalDateTime currentTime = LocalDateTime.of(2024, 1, 15, 10, 0); // Monday 10:00
        LocalDateTime lastNotificationTime = LocalDateTime.of(2024, 1, 15, 8, 0); // 2 hours ago

        // When
        ReceptionRulesEngine.NotificationScheduleResult result = receptionRulesEngine
                .evaluateNotificationSchedule(testAlert, defaultSettings, lastNotificationTime, currentTime);

        // Then
        assertNotNull(result);
        // The exact result depends on the implementation details
    }

    @Test
    void testCalculateLastReceptionPeriodEnd_NoReceptionPeriod() {
        // Given
        ReceptionSettingsDto settings = new ReceptionSettingsDto(
                "DAILY", 60, null, false, defaultSettings.receptionMethods(), false);
        LocalDateTime currentTime = LocalDateTime.of(2024, 1, 15, 20, 0); // Monday 20:00

        // When
        LocalDateTime result = receptionRulesEngine.calculateLastReceptionPeriodEnd(settings, currentTime);

        // Then
        assertEquals(currentTime.minusMinutes(60), result);
    }

    @Test
    void testCalculateLastReceptionPeriodEnd_SameDayPeriod() {
        // Given - 09:00-18:00 reception period, current time is 20:00 (outside period)
        LocalDateTime currentTime = LocalDateTime.of(2024, 1, 15, 20, 0); // Monday 20:00

        // When
        LocalDateTime result = receptionRulesEngine.calculateLastReceptionPeriodEnd(defaultSettings, currentTime);

        // Then - Should return today's 18:00
        LocalDateTime expected = LocalDateTime.of(2024, 1, 15, 18, 0);
        assertEquals(expected, result);
    }

    @Test
    void testCalculateLastReceptionPeriodEnd_CrossDayPeriod() {
        // Given - 22:00-06:00 reception period (cross day)
        ReceptionSettingsDto.TimePeriodDto crossDayPeriod = new ReceptionSettingsDto.TimePeriodDto("22:00", "06:00");
        ReceptionSettingsDto settings = new ReceptionSettingsDto(
                "DAILY", 30, crossDayPeriod, false, defaultSettings.receptionMethods(), false);
        LocalDateTime currentTime = LocalDateTime.of(2024, 1, 15, 20, 0); // Monday 20:00

        // When
        LocalDateTime result = receptionRulesEngine.calculateLastReceptionPeriodEnd(settings, currentTime);

        // Then - Should return today's 06:00 (end of last night's period)
        LocalDateTime expected = LocalDateTime.of(2024, 1, 15, 6, 0);
        assertEquals(expected, result);
    }

    @Test
    void testCalculateLastReceptionPeriodEnd_WorkdaysOnly() {
        // Given - WORKDAYS only, current time is Saturday
        ReceptionSettingsDto settings = new ReceptionSettingsDto(
                "WORKDAYS", 30, defaultSettings.receptionPeriod(), false, defaultSettings.receptionMethods(), false);
        LocalDateTime currentTime = LocalDateTime.of(2024, 1, 13, 20, 0); // Saturday 20:00

        // When
        LocalDateTime result = receptionRulesEngine.calculateLastReceptionPeriodEnd(settings, currentTime);

        // Then - Should return Friday's 18:00
        LocalDateTime expected = LocalDateTime.of(2024, 1, 12, 18, 0); // Friday 18:00
        assertEquals(expected, result);
    }

    @Test
    void testCalculateLastReceptionPeriodEnd_HolidaysOnly() {
        // Given - HOLIDAYS only, current time is Monday
        ReceptionSettingsDto settings = new ReceptionSettingsDto(
                "HOLIDAYS", 30, defaultSettings.receptionPeriod(), false, defaultSettings.receptionMethods(), false);
        LocalDateTime currentTime = LocalDateTime.of(2024, 1, 15, 20, 0); // Monday 20:00

        // When
        LocalDateTime result = receptionRulesEngine.calculateLastReceptionPeriodEnd(settings, currentTime);

        // Then - Should return Sunday's 18:00
        LocalDateTime expected = LocalDateTime.of(2024, 1, 14, 18, 0); // Sunday 18:00
        assertEquals(expected, result);
    }

    @Test
    void testCalculateLastReceptionPeriodEnd_With24Hour() {
        // Given - 09:00-24:00 reception period
        ReceptionSettingsDto.TimePeriodDto period24 = new ReceptionSettingsDto.TimePeriodDto("09:00", "24:00");
        ReceptionSettingsDto settings = new ReceptionSettingsDto(
                "DAILY", 30, period24, false, defaultSettings.receptionMethods(), false);
        LocalDateTime currentTime = LocalDateTime.of(2024, 1, 15, 8, 0); // Monday 08:00

        // When
        LocalDateTime result = receptionRulesEngine.calculateLastReceptionPeriodEnd(settings, currentTime);

        // Then - Should return yesterday's 23:59:59 (24:00 converted)
        LocalDateTime expected = LocalDateTime.of(2024, 1, 14, 23, 59, 59);
        assertEquals(expected, result);
    }
}
