package com.czb.hn.service.business.impl;

import com.czb.hn.dto.PlanDTO;
import com.czb.hn.dto.alert.AlertConfigurationResponseDto;
import com.czb.hn.dto.alert.config.ReceptionSettingsDto;
import com.czb.hn.enums.InformationSensitivityType;
import com.czb.hn.jpa.securadar.entity.AlertNotificationQueue;
import com.czb.hn.jpa.securadar.entity.AlertResult;
import com.czb.hn.enums.NotificationStatus;
import com.czb.hn.enums.NotificationType;
import com.czb.hn.jpa.securadar.repository.AlertNotificationQueueRepository;
import com.czb.hn.jpa.securadar.repository.AlertResultRepository;
import com.czb.hn.service.business.AlertConfigurationConsumerService;
import com.czb.hn.service.business.AlertNotificationScheduler;
import com.czb.hn.service.business.AlertPushService;
import com.czb.hn.service.business.PlanService;
import com.czb.hn.service.business.ReceptionRulesEngine;

import com.czb.hn.util.JsonUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Alert Notification Scheduler Implementation
 * Handles the scheduling and processing of alert notifications based on
 * reception settings
 */
@Service
@Slf4j
public class AlertNotificationSchedulerImpl implements AlertNotificationScheduler {

    @Value("${alert.message.sms.template.content}")
    private String smsContentTemplate;
    @Value("${alert.message.sms.template.noWarnContent}")
    private String smsNoWarnContentTemplate;
    @Value("${alert.message.email.template.subjectContent}")
    private String emailSubjectTemplate;
    @Value("${alert.message.email.template.noWarnSubjectContent}")
    private String emailNoWarnSubjectTemplate;
    @Value("${alert.message.email.template.content}")
    private String emailContentTemplate;
    @Value("${alert.message.email.template.noWarnContent}")
    private String emailNoWarnContentTemplate;
    @Value("${alert.message.webHookUrl}")
    private String webHookUrl;

    @Autowired
    private AlertNotificationQueueRepository notificationQueueRepository;

    @Autowired
    private AlertResultRepository alertResultRepository;

    @Autowired
    private AlertConfigurationConsumerService alertConfigConsumerService;

    @Autowired
    private ReceptionRulesEngine receptionRulesEngine;

    @Autowired
    private AlertPushService alertPushService;

    @Autowired
    private PlanService planService;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    @Scheduled(fixedDelay = 60000) // Every minute
    public void processPendingNotifications() {
        log.debug("Starting to process pending notifications");

        try {
            // Process ready notifications
            int processedCount = processReadyNotifications();

            // Retry failed notifications
            int retriedCount = retryFailedNotifications();

            // Schedule new notifications for recent alerts
            int scheduledCount = scheduleNewNotifications();

            if (processedCount > 0 || retriedCount > 0 ||
                    scheduledCount > 0) {
                log.info("Notification processing summary - Processed: {}, Retried: {}, , Scheduled: {}",
                        processedCount, retriedCount, scheduledCount);
            }

        } catch (Exception e) {
            log.error("Error in notification processing cycle", e);
        }
    }

    @Override
    public void scheduleNoAlertNotifications(AlertConfigurationResponseDto configuration) {
        log.debug("Checking no-alert notifications for configuration: {}", configuration.id());

        try {
            if (configuration.receptionSettings() == null ||
                    configuration.receptionSettings().noAlertNotification() == null ||
                    !configuration.receptionSettings().noAlertNotification()) {
                return; // No-alert notifications not enabled
            }

            // Check if there were any alerts in the last interval period
            LocalDateTime intervalStart = LocalDateTime.now().minusMinutes(
                    configuration.receptionSettings().alertInterval() != null
                            ? configuration.receptionSettings().alertInterval()
                            : 60);

            boolean hasRecentAlerts = alertResultRepository.existsByConfigurationIdAndWarningTimeBetween(
                    configuration.id(), intervalStart, LocalDateTime.now());

            if (hasRecentAlerts) {
                log.debug("Recent alerts found for configuration {}, no-alert notification not needed",
                        configuration.id());
                return;
            }

            // Check if no-alert notification already scheduled for this period
            if (notificationQueueRepository.existsNoAlertNotificationInTimeRange(
                    configuration.id(), intervalStart, LocalDateTime.now().plusHours(1))) {
                log.debug("No-alert notification already scheduled for configuration {}", configuration.id());
                return;
            }

            // Get last notification time
            LocalDateTime lastNotificationTime = getLastNotificationTime(configuration.id());

            // Evaluate notification schedule for no-alert case
            ReceptionRulesEngine.NotificationScheduleResult scheduleResult = receptionRulesEngine
                    .evaluateNotificationSchedule(
                            null, configuration.receptionSettings(), lastNotificationTime, LocalDateTime.now());

            if (!scheduleResult.shouldSchedule() || scheduleResult.notificationType() != NotificationType.NO_ALERT) {
                log.debug("No-alert notification not scheduled for configuration {}: {}",
                        configuration.id(), scheduleResult.reason());
                return;
            }

            // Extract recipients
            List<ReceptionRulesEngine.RecipientInfo> recipients = receptionRulesEngine
                    .extractRecipients(configuration.receptionSettings());

            if (recipients.isEmpty()) {
                log.warn("No recipients found for no-alert notification, configuration: {}", configuration.id());
                return;
            }

            // Create no-alert notification queue entry
            AlertNotificationQueue notification = AlertNotificationQueue.builder()
                    .planId(configuration.planId())
                    .configurationId(configuration.id())
                    .enterpriseId(configuration.enterpriseId())
                    .scheduledTime(scheduleResult.scheduledTime())
                    .status(NotificationStatus.PENDING)
                    .recipients(serializeRecipients(recipients))
                    .notificationType(NotificationType.NO_ALERT)
                    .receptionSettings(serializeReceptionSettings(configuration.receptionSettings()))
                    .createdBy("SCHEDULER")
                    .build();

            notificationQueueRepository.save(notification);
            log.info("Scheduled no-alert notification for configuration {} at {}",
                    configuration.id(), scheduleResult.scheduledTime());

        } catch (Exception e) {
            log.error("Failed to schedule no-alert notifications for configuration {}", configuration.id(), e);
        }
    }

    @Override
    public int processReadyNotifications() {
        log.debug("Processing ready notifications");

        try {
            List<AlertNotificationQueue> readyNotifications = notificationQueueRepository
                    .findReadyToProcess(NotificationStatus.PENDING, LocalDateTime.now());

            int processedCount = 0;
            for (AlertNotificationQueue notification : readyNotifications) {
                try {
                    processNotification(notification);
                    processedCount++;
                } catch (Exception e) {
                    log.error("Failed to process notification {}", notification.getId(), e);
                    notification.markAsFailed("Processing error: " + e.getMessage());
                    notificationQueueRepository.save(notification);
                }
            }

            return processedCount;

        } catch (Exception e) {
            log.error("Error processing ready notifications", e);
            return 0;
        }
    }

    @Override
    public int retryFailedNotifications() {
        log.debug("Retrying failed notifications");

        try {
            List<AlertNotificationQueue> retryNotifications = notificationQueueRepository
                    .findReadyForRetry(NotificationStatus.FAILED, LocalDateTime.now());

            int retriedCount = 0;
            for (AlertNotificationQueue notification : retryNotifications) {
                try {
                    processNotification(notification);
                    retriedCount++;
                } catch (Exception e) {
                    log.error("Failed to retry notification {}", notification.getId(), e);
                    notification.markAsFailed("Retry error: " + e.getMessage());
                    notificationQueueRepository.save(notification);
                }
            }

            return retriedCount;

        } catch (Exception e) {
            log.error("Error retrying failed notifications", e);
            return 0;
        }
    }

    /**
     * Process a single notification
     */
    private void processNotification(AlertNotificationQueue notification) {
        log.debug("Processing notification {}", notification.getId());

        try {
            // Mark as processing
            notification.markAsProcessing();
            notificationQueueRepository.save(notification);

            // Deserialize recipients
            List<ReceptionRulesEngine.RecipientInfo> recipients = deserializeRecipients(notification.getRecipients());

            // Process based on notification type
            if (notification.isNoAlertNotification()) {
                processNoAlertNotification(notification, recipients);
            } else {
                processAlertNotification(notification, recipients);
            }

            // Mark as completed
            notification.markAsCompleted();
            notificationQueueRepository.save(notification);

            log.info("Successfully processed notification {}", notification.getId());

        } catch (Exception e) {
            log.error("Failed to process notification {}", notification.getId(), e);
            notification.markAsFailed("Processing failed: " + e.getMessage());
            notificationQueueRepository.save(notification);
            throw e;
        }
    }

    /**
     * Process no-alert notification
     */
    private void processNoAlertNotification(AlertNotificationQueue notification,
            List<ReceptionRulesEngine.RecipientInfo> recipients) {

        log.debug("Processing no-alert notification for configuration {}", notification.getConfigurationId());

        // acquire plan
        Long planId = notification.getPlanId();
        if (planId == null) {
            log.error("notification is not plan id");
            return;
        }
        PlanDTO plan = planService.getPlanById(planId);

        // 反序列化接收设置
        ReceptionSettingsDto receptionSettings = null;
        if (notification.getReceptionSettings() != null) {
            try {
                receptionSettings = JsonUtil.fromJson(notification.getReceptionSettings(), ReceptionSettingsDto.class);
            } catch (Exception e) {
                log.warn("Failed to deserialize reception settings for no-alert notification {}, using default",
                        notification.getId(), e);
                // 使用默认设置
                ReceptionSettingsDto.EmailConfigDto emailConfig = new ReceptionSettingsDto.EmailConfigDto(false, null);
                ReceptionSettingsDto.SmsConfigDto smsConfig = new ReceptionSettingsDto.SmsConfigDto(true, null);
                ReceptionSettingsDto.ReceptionMethodsDto receptionMethods = new ReceptionSettingsDto.ReceptionMethodsDto(
                        emailConfig, smsConfig);
                receptionSettings = new ReceptionSettingsDto("DAILY", 30, null, false, receptionMethods, false);
            }
        } else {
            // 如果没有设置，使用默认值
            ReceptionSettingsDto.EmailConfigDto emailConfig = new ReceptionSettingsDto.EmailConfigDto(false, null);
            ReceptionSettingsDto.SmsConfigDto smsConfig = new ReceptionSettingsDto.SmsConfigDto(true, null);
            ReceptionSettingsDto.ReceptionMethodsDto receptionMethods = new ReceptionSettingsDto.ReceptionMethodsDto(
                    emailConfig, smsConfig);
            receptionSettings = new ReceptionSettingsDto("DAILY", 30, null, false, receptionMethods, false);
        }

        // 计算无预警通知的时间段
        LocalDateTime currentTime = LocalDateTime.now();
        ReceptionRulesEngine.TimeRangeResult timeRange = receptionRulesEngine.calculateAlertTimeRange(
                NotificationType.NO_ALERT, receptionSettings, currentTime, notification.getCreatedAt());

        // Process push notifications for each recipient
        for (ReceptionRulesEngine.RecipientInfo recipient : recipients) {
            try {
                if (recipient.emailEnabled() && recipient.email() != null) {
                    String subject = createNoAlertEmailSubject(plan, timeRange);
                    String content = createNoAlertEmailContent(plan, timeRange);
                    alertPushService.sendEmailNotification(recipient.email(), subject, content);
                }

                if (recipient.smsEnabled() && recipient.phone() != null) {
                    String message = createNoAlertSmsMessage(plan, timeRange);
                    alertPushService.sendSmsNotification(recipient.phone(), message);
                }

                // Create push record for no-alert notification
                alertPushService.createNoAlertPushRecord(notification, recipient);

            } catch (Exception e) {
                log.error("Failed to send no-alert notification to recipient {}", recipient.name(), e);
            }
        }

        log.info("Completed processing no-alert notification for plan {} with time range {}",
                plan.name(), timeRange.formattedRange());
    }

    /**
     * Process alert-based notification
     */
    private void processAlertNotification(AlertNotificationQueue notification,
            List<ReceptionRulesEngine.RecipientInfo> recipients) {

        log.debug("Processing batch alert notification {}", notification.getId());

        // Get all alerts associated with this notification
        List<AlertResult> alerts = alertResultRepository.findByAlertNotificationQueueId(notification.getId());

        if (alerts.isEmpty()) {
            log.warn("No alerts found for notification queue {}", notification.getId());
            return;
        }

        log.info("Processing batch notification for {} alerts", alerts.size());

        // acquire plan
        Long planId = notification.getPlanId();
        if (planId == null) {
            log.error("notification is not plan id");
            return;
        }
        PlanDTO plan = planService.getPlanById(planId);

        // 反序列化接收设置
        ReceptionSettingsDto receptionSettings = null;
        if (notification.getReceptionSettings() != null) {
            try {
                receptionSettings = JsonUtil.fromJson(notification.getReceptionSettings(), ReceptionSettingsDto.class);
            } catch (Exception e) {
                log.warn("Failed to deserialize reception settings for notification {}, using default interval",
                        notification.getId(), e);
                // 使用默认设置
                receptionSettings = new ReceptionSettingsDto("DAILY", 30, null, false, null, false);
            }
        } else {
            // 如果没有设置，使用默认值
            receptionSettings = new ReceptionSettingsDto("DAILY", 30, null, false, null, false);
        }

        // Send one notification per recipient type (SMS/Email), but create push records
        // for each alert
        for (ReceptionRulesEngine.RecipientInfo recipient : recipients) {
            try {
                // Send batch notification (one SMS/Email for all alerts)
                if (recipient.smsEnabled() && recipient.phone() != null) {
                    String batchMessage = createBatchSmsMessage(alerts, plan);
                    alertPushService.sendSmsNotification(recipient.phone(), batchMessage);
                }

                if (recipient.emailEnabled() && recipient.email() != null) {
                    String batchSubject = createBatchEmailSubject(alerts, plan);
                    String batchContent = createBatchEmailContent(alerts, plan, receptionSettings);
                    alertPushService.sendEmailNotification(recipient.email(), batchSubject, batchContent);
                }

                // Create push records for each alert
                for (AlertResult alert : alerts) {
                    alertPushService.createPushRecordsForAlert(alert, recipient);
                }

            } catch (Exception e) {
                log.error("Failed to send batch notification to recipient {}", recipient.name(), e);
            }
        }
    }

    /**
     * Get last notification time for a configuration
     */
    private LocalDateTime getLastNotificationTime(Long configurationId) {
        try {
            // 查询该配置最近的通知时间
            return notificationQueueRepository.findTopByConfigurationIdOrderByScheduledTimeDesc(configurationId)
                    .map(AlertNotificationQueue::getScheduledTime)
                    .orElse(null);
        } catch (Exception e) {
            log.warn("Failed to get last notification time for configuration {}: {}", configurationId, e.getMessage());
            return null;
        }
    }

    /**
     * Create batch SMS message for multiple alerts
     */
    private String createBatchSmsMessage(List<AlertResult> alerts, PlanDTO planDTO) {

        // "{address} 监控方案 {planName} 新增{number}条预警"
        return String.format(smsContentTemplate,
                webHookUrl,
                planDTO.name(), alerts.size());
    }

    /**
     * Create batch email subject for multiple alerts
     */
    private String createBatchEmailSubject(List<AlertResult> alerts, PlanDTO planDTO) {
        // 取最新一条预警标题
        AlertResult alert = alerts.getFirst();
        String title = alert.getTitle();
        String name = planDTO.name();
        return String.format(emailSubjectTemplate, name, alerts.size(), title);
    }

    /**
     * Create batch email content for multiple alerts
     */
    private String createBatchEmailContent(List<AlertResult> alerts, PlanDTO planDTO,
            ReceptionSettingsDto receptionSettings) {
        StringBuilder content = new StringBuilder();

        String name = planDTO.name();

        // 获取预警时间段，使用ReceptionRulesEngine计算
        LocalDateTime now = LocalDateTime.now();
        ReceptionRulesEngine.TimeRangeResult timeRange = receptionRulesEngine.calculateAlertTimeRange(
                NotificationType.ALERT, receptionSettings, now, null);
        String timeRangeStr = timeRange.formattedRange();

        int size = alerts.size();

        // 敏感信息占比
        long sensitiveCount = alerts.stream()
                .filter(it -> InformationSensitivityType.SENSITIVE.equals(it.getInformationSensitivityType()))
                .count();
        double sensitiveRatio = 0.0;
        String sensitiveRatioPercentage;

        if (size > 0) {
            sensitiveRatio = (double) sensitiveCount / size;
            sensitiveRatioPercentage = String.format("%.1f%%", sensitiveRatio * 100);
        } else {
            sensitiveRatioPercentage = "0.0%";
        }

        content.append(
                String.format(emailContentTemplate, name, timeRangeStr, name, timeRangeStr, size,
                        sensitiveRatioPercentage));

        // 中等及严重预警占比：%中等及严重预警占比%\n\n\t更多相关信息请点击查看：查看详情
        long warningNum = alerts.stream().filter(it -> AlertResult.WarningLevel.SEVERE.equals(it.getWarningLevel()) ||
                AlertResult.WarningLevel.MODERATE.equals(it.getWarningLevel())).count();

        if (warningNum > 0) {
            // 中等以及严重比例占比
            double warnRatio = (double) warningNum / size; // 修复bug：应该是warningNum而不是warnRatio
            String warnRatioPercentage = String.format("%.1f%%", warnRatio * 100);

            content.append(String.format("中等及严重预警占比：%s\n\t", warnRatioPercentage));
        }

        content.append(String.format("更多相关信息请点击查看: %s", webHookUrl));

        return content.toString();
    }

    /**
     * 检查当前时间是否在接收时段内
     */
    private boolean isWithinReceptionPeriod(ReceptionSettingsDto receptionSettings, LocalDateTime now) {
        if (receptionSettings.receptionPeriod() == null) {
            return true; // 如果没有设置时段，默认全天接收
        }

        String startTime = receptionSettings.receptionPeriod().start();
        String endTime = receptionSettings.receptionPeriod().end();

        if (startTime == null || endTime == null) {
            return true;
        }

        try {
            String[] startParts = startTime.split(":");
            String[] endParts = endTime.split(":");

            int startHour = Integer.parseInt(startParts[0]);
            int startMinute = Integer.parseInt(startParts[1]);
            int endHour = Integer.parseInt(endParts[0]);
            int endMinute = Integer.parseInt(endParts[1]);

            // 处理24:00的情况
            if (endHour == 24) {
                endHour = 0;
                endMinute = 0;
            }

            int currentHour = now.getHour();
            int currentMinute = now.getMinute();
            int currentTotalMinutes = currentHour * 60 + currentMinute;
            int startTotalMinutes = startHour * 60 + startMinute;
            int endTotalMinutes = endHour * 60 + endMinute;

            // 处理跨天的情况
            if (endTotalMinutes <= startTotalMinutes) {
                // 跨天：例如22:00-06:00
                return currentTotalMinutes >= startTotalMinutes || currentTotalMinutes <= endTotalMinutes;
            } else {
                // 同一天：例如09:00-18:00
                return currentTotalMinutes >= startTotalMinutes && currentTotalMinutes <= endTotalMinutes;
            }

        } catch (Exception e) {
            log.warn("Failed to parse reception period: {} - {}, defaulting to true", startTime, endTime, e);
            return true;
        }
    }

    /**
     * 检查接收时间类型是否匹配
     */
    private boolean isValidReceptionTime(String receptionTime, LocalDateTime now) {
        if (receptionTime == null || "DAILY".equals(receptionTime)) {
            return true;
        }

        int dayOfWeek = now.getDayOfWeek().getValue(); // 1=Monday, 7=Sunday

        switch (receptionTime) {
            case "WORKDAYS":
                return dayOfWeek >= 1 && dayOfWeek <= 5; // Monday to Friday
            case "HOLIDAYS":
                return dayOfWeek == 6 || dayOfWeek == 7; // Saturday and Sunday
            default:
                log.warn("Unknown reception time type: {}, defaulting to true", receptionTime);
                return true;
        }
    }

    /**
     * 创建批量通知
     * 按plan_id分组: 同一计划的多个预警合并为一个通知
     */
    private int createBatchNotification(List<AlertResult> alerts, NotificationType notificationType) {
        if (alerts.isEmpty()) {
            return 0;
        }

        try {
            // 按plan_id分组处理
            Map<Long, List<AlertResult>> alertsByPlan = alerts.stream()
                    .collect(Collectors.groupingBy(AlertResult::getPlanId));

            int createdCount = 0;

            for (Map.Entry<Long, List<AlertResult>> entry : alertsByPlan.entrySet()) {
                Long planId = entry.getKey();
                List<AlertResult> planAlerts = entry.getValue();

                // 获取第一个预警的配置ID和企业ID（同一批次应该是相同的配置）
                Long configurationId = planAlerts.getFirst().getConfigurationId();
                String enterpriseId = planAlerts.getFirst().getEnterpriseId();

                Optional<AlertConfigurationResponseDto> alertConfigOp = alertConfigConsumerService
                        .getActiveConfigurationById(configurationId);
                if (alertConfigOp.isEmpty()) {
                    log.error("not find alert config {}", configurationId);
                    return 0;
                }
                AlertConfigurationResponseDto alertConfigurationResponseDto = alertConfigOp.get();
                ReceptionSettingsDto receptionSettingsDto = alertConfigurationResponseDto.receptionSettings();
                List<ReceptionRulesEngine.RecipientInfo> recipientInfos = receptionRulesEngine
                        .extractRecipients(receptionSettingsDto);
                String receptionSettingsJson = objectMapper.writeValueAsString(receptionSettingsDto);

                // 创建通知队列记录
                AlertNotificationQueue notification = AlertNotificationQueue.builder()
                        .planId(planId)
                        .enterpriseId(enterpriseId)
                        .configurationId(configurationId)
                        .notificationType(notificationType)
                        .status(NotificationStatus.PENDING)
                        .receptionSettings(receptionSettingsJson)
                        .recipients(serializeRecipients(recipientInfos))
                        .scheduledTime(LocalDateTime.now())
                        .createdAt(LocalDateTime.now())
                        .createdBy("system")
                        .build();

                // 保存通知队列
                notification = notificationQueueRepository.save(notification);

                // 关联所有预警到这个通知队列
                for (AlertResult alert : planAlerts) {
                    alert.setAlertNotificationQueueId(notification.getId());
                }
                alertResultRepository.saveAll(planAlerts);

                createdCount++;
                log.debug("Created batch notification {} for {} alerts in plan {}",
                        notification.getId(), planAlerts.size(), planId);
            }

            return createdCount;

        } catch (Exception e) {
            log.error("Failed to create batch notification for {} alerts", alerts.size(), e);
            return 0;
        }
    }

    /**
     * Schedule new notifications for recent alerts
     * 根据预警接收配置的接收时间、时间间隔、接收时段、信息补推、无预警通知等配置进行创建提醒
     */
    private int scheduleNewNotifications() {
        log.info("Starting to schedule new notifications based on reception settings");

        int scheduledCount = 0;

        try {
            // 获取所有活跃的预警配置
            List<AlertConfigurationResponseDto> activeConfigurations = alertConfigConsumerService
                    .getAllActiveConfigurations();

            for (AlertConfigurationResponseDto configuration : activeConfigurations) {
                try {
                    scheduledCount += processConfigurationNotifications(configuration);
                } catch (Exception e) {
                    log.error("Failed to process notifications for configuration {}",
                            configuration.id(), e);
                }
            }

            log.info("Completed scheduling new notifications. Total scheduled: {}", scheduledCount);
            return scheduledCount;

        } catch (Exception e) {
            log.error("Failed to schedule new notifications", e);
            return scheduledCount;
        }
    }

    /**
     * 处理单个配置的通知调度
     */
    private int processConfigurationNotifications(AlertConfigurationResponseDto configuration) {
        int count = 0;
        ReceptionSettingsDto receptionSettings = configuration.receptionSettings();

        if (receptionSettings == null) {
            log.debug("No reception settings for configuration {}", configuration.id());
            return 0;
        }

        LocalDateTime now = LocalDateTime.now();

        // 1. 处理常规预警通知
        count += scheduleAlertNotifications(configuration, now);

        // 2. 处理信息补推通知
        if (Boolean.TRUE.equals(receptionSettings.infoPush())) {
            count += scheduleInfoPushNotifications(configuration, now);
        }

        // 3. 处理无预警通知
        if (Boolean.TRUE.equals(receptionSettings.noAlertNotification())) {
            count += scheduleNoAlertNotifications(configuration, now);
        }

        return count;
    }

    /**
     * 处理常规预警通知调度
     * 根据接收时间、时间间隔、接收时段配置进行调度
     */
    private int scheduleAlertNotifications(AlertConfigurationResponseDto configuration, LocalDateTime now) {
        int count = 0;
        ReceptionSettingsDto receptionSettings = configuration.receptionSettings();

        // 检查当前时间是否在接收时段内
        if (!isWithinReceptionPeriod(receptionSettings, now)) {
            log.debug("Current time {} is outside reception period for configuration {}",
                    now, configuration.id());
            return 0;
        }

        // 检查接收时间类型（DAILY, WORKDAYS, HOLIDAYS）
        if (!isValidReceptionTime(receptionSettings.receptionTime(), now)) {
            log.debug("Current time {} does not match reception time {} for configuration {}",
                    now, receptionSettings.receptionTime(), configuration.id());
            return 0;
        }

        // 查找该配置下需要通知的预警
        LocalDateTime intervalStart = now.minusMinutes(receptionSettings.alertInterval());
        List<AlertResult> pendingAlerts = alertResultRepository
                .findUnnotifiedAlertsByConfigurationAndTimeRange(
                        configuration.id(), intervalStart, now);

        if (!pendingAlerts.isEmpty()) {
            count += createBatchNotification(pendingAlerts, NotificationType.ALERT);
            log.debug("Scheduled {} alert notifications for configuration {}",
                    count, configuration.id());
        }

        return count;
    }

    /**
     * 处理信息补推通知调度
     * 信息补推开启后，将推送非预警时间段内符合预警条件的信息
     */
    private int scheduleInfoPushNotifications(AlertConfigurationResponseDto configuration, LocalDateTime now) {
        int count = 0;
        ReceptionSettingsDto receptionSettings = configuration.receptionSettings();

        // 检查当前时间是否在非预警时间段内
        if (isWithinReceptionPeriod(receptionSettings, now)) {
            log.debug("Current time {} is within reception period, skipping info push for configuration {}",
                    now, configuration.id());
            return 0;
        }

        // 查找非预警时间段内符合条件的信息
        // todo intervalStart 计算存在问题 间隔用不太上，信息补推是补推非接收时间段的预警,
        // 要考虑接收时间和接收时段两位变量，特别注意接收时段后，周末后，节假日后
        LocalDateTime intervalStart = now.minusMinutes(receptionSettings.alertInterval());
        List<AlertResult> infoPushAlerts = alertResultRepository
                .findInfoPushAlertsByConfigurationAndTimeRange(
                        configuration.id(), intervalStart, now);

        if (!infoPushAlerts.isEmpty()) {
            count += createBatchNotification(infoPushAlerts, NotificationType.INFO_PUSH);
            log.debug("Scheduled {} info push notifications for configuration {}",
                    count, configuration.id());
        }

        return count;
    }

    /**
     * 处理无预警通知调度
     * 开启后，前一日21:00至当日21:00无预警信息时会下发提示
     */
    private int scheduleNoAlertNotifications(AlertConfigurationResponseDto configuration, LocalDateTime now) {
        // 检查是否到了21:00点
        if (now.getHour() != 21 || now.getMinute() != 0) {
            return 0;
        }

        // 检查前一日21:00至当日21:00是否有预警
        LocalDateTime yesterday21 = now.minusDays(1).withHour(21).withMinute(0).withSecond(0);
        LocalDateTime today21 = now.withHour(21).withMinute(0).withSecond(0);

        long alertCount = alertResultRepository
                .countAlertsByConfigurationAndTimeRange(
                        configuration.id(), yesterday21, today21);

        if (alertCount == 0) {
            // 创建无预警通知
            scheduleNoAlertNotifications(configuration);
            return 1;
        }

        return 0;
    }

    /**
     * Serialize recipients to JSON
     */
    private String serializeRecipients(List<ReceptionRulesEngine.RecipientInfo> recipients) {
        try {
            return objectMapper.writeValueAsString(recipients);
        } catch (Exception e) {
            log.error("Failed to serialize recipients", e);
            return "[]";
        }
    }

    /**
     * Deserialize recipients from JSON
     */
    private List<ReceptionRulesEngine.RecipientInfo> deserializeRecipients(String recipientsJson) {
        try {
            return objectMapper.readValue(recipientsJson,
                    objectMapper.getTypeFactory().constructCollectionType(List.class,
                            ReceptionRulesEngine.RecipientInfo.class));
        } catch (Exception e) {
            log.error("Failed to deserialize recipients", e);
            return new ArrayList<>();
        }
    }

    /**
     * Serialize reception settings to JSON
     */
    private String serializeReceptionSettings(Object receptionSettings) {
        try {
            return objectMapper.writeValueAsString(receptionSettings);
        } catch (Exception e) {
            log.error("Failed to serialize reception settings", e);
            return "{}";
        }
    }

    /**
     * Create no-alert SMS message
     */
    private String createNoAlertSmsMessage(PlanDTO planDTO, ReceptionRulesEngine.TimeRangeResult timeRange) {
        return String.format(smsNoWarnContentTemplate, planDTO.name(), timeRange.formattedRange());
    }

    /**
     * Create no-alert email subject
     */
    private String createNoAlertEmailSubject(PlanDTO planDTO, ReceptionRulesEngine.TimeRangeResult timeRange) {
        return String.format(emailNoWarnSubjectTemplate, planDTO.name(), timeRange.formattedRange());
    }

    /**
     * Create no-alert email content
     */
    private String createNoAlertEmailContent(PlanDTO planDTO, ReceptionRulesEngine.TimeRangeResult timeRange) {
        return String.format(emailNoWarnContentTemplate, timeRange.formattedRange(), planDTO.name(), webHookUrl);
    }

}
