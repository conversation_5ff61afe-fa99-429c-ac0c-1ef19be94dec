package com.czb.hn.service.business;

import com.czb.hn.dto.alert.config.ReceptionSettingsDto;
import com.czb.hn.jpa.securadar.entity.AlertResult;
import com.czb.hn.enums.NotificationType;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Reception Rules Engine Interface
 * Evaluates reception settings to determine when and how alerts should be sent
 */
public interface ReceptionRulesEngine {

    /**
     * Determine if a notification should be sent for an alert
     * 
     * @param alert                Alert result to evaluate
     * @param settings             Reception settings to apply
     * @param lastNotificationTime Time of last notification for this configuration
     * @return true if notification should be sent
     */
    boolean shouldSendNotification(
            AlertResult alert,
            ReceptionSettingsDto settings,
            LocalDateTime lastNotificationTime);

    /**
     * Calculate the next scheduled notification time
     * 
     * @param settings             Reception settings
     * @param currentTime          Current time
     * @param lastNotificationTime Time of last notification
     * @return Next scheduled notification time
     */
    LocalDateTime calculateNextNotificationTime(
            ReceptionSettingsDto settings,
            LocalDateTime currentTime,
            LocalDateTime lastNotificationTime);

    /**
     * Determine the type of notification to send
     * 
     * @param alert               Alert result (null for no-alert notifications)
     * @param settings            Reception settings
     * @param isWithinAlertPeriod Whether current time is within alert reception
     *                            period
     * @return Notification type
     */
    NotificationType determineNotificationType(
            AlertResult alert,
            ReceptionSettingsDto settings,
            boolean isWithinAlertPeriod);

    /**
     * Check if current time is within reception period
     * 
     * @param settings    Reception settings
     * @param currentTime Current time to check
     * @return true if within reception period
     */
    boolean isWithinReceptionPeriod(ReceptionSettingsDto settings, LocalDateTime currentTime);

    /**
     * Check if current time matches reception time schedule
     * 
     * @param settings    Reception settings
     * @param currentTime Current time to check
     * @return true if matches reception schedule
     */
    boolean matchesReceptionSchedule(ReceptionSettingsDto settings, LocalDateTime currentTime);

    /**
     * Extract recipient information from reception settings
     * 
     * @param settings Reception settings
     * @return List of recipients with their contact information
     */
    List<RecipientInfo> extractRecipients(ReceptionSettingsDto settings);

    /**
     * Check if enough time has passed since last notification (interval check)
     * 
     * @param settings             Reception settings
     * @param lastNotificationTime Time of last notification
     * @param currentTime          Current time
     * @return true if interval requirement is met
     */
    boolean hasIntervalPassed(
            ReceptionSettingsDto settings,
            LocalDateTime lastNotificationTime,
            LocalDateTime currentTime);

    /**
     * Validate reception settings
     * 
     * @param settings Reception settings to validate
     * @throws IllegalArgumentException if settings are invalid
     */
    void validateReceptionSettings(ReceptionSettingsDto settings);

    /**
     * Recipient information extracted from reception settings
     */
    record RecipientInfo(
            String name,
            String email,
            String phone,
            String username,
            boolean emailEnabled,
            boolean smsEnabled) {
        /**
         * Check if this recipient has any enabled notification methods
         */
        public boolean hasEnabledMethods() {
            return emailEnabled || smsEnabled;
        }

        /**
         * Get enabled notification methods count
         */
        public int getEnabledMethodsCount() {
            int count = 0;
            if (emailEnabled)
                count++;
            if (smsEnabled)
                count++;
            return count;
        }
    }

    /**
     * Notification scheduling result
     */
    record NotificationScheduleResult(
            boolean shouldSchedule,
            LocalDateTime scheduledTime,
            NotificationType notificationType,
            String reason) {
        /**
         * Create a result indicating notification should be scheduled
         */
        public static NotificationScheduleResult schedule(
                LocalDateTime scheduledTime,
                NotificationType notificationType) {
            return new NotificationScheduleResult(true, scheduledTime, notificationType, null);
        }

        /**
         * Create a result indicating notification should not be scheduled
         */
        public static NotificationScheduleResult skip(String reason) {
            return new NotificationScheduleResult(false, null, null, reason);
        }
    }

    /**
     * Time range calculation result
     */
    record TimeRangeResult(
            LocalDateTime startTime,
            LocalDateTime endTime,
            String formattedRange) {

        public static TimeRangeResult of(LocalDateTime startTime, LocalDateTime endTime, String formattedRange) {
            return new TimeRangeResult(startTime, endTime, formattedRange);
        }
    }

    /**
     * Evaluate complete notification scheduling logic
     *
     * @param alert                Alert result (null for no-alert checks)
     * @param settings             Reception settings
     * @param lastNotificationTime Time of last notification
     * @param currentTime          Current time
     * @return Scheduling result with decision and timing
     */
    NotificationScheduleResult evaluateNotificationSchedule(
            AlertResult alert,
            ReceptionSettingsDto settings,
            LocalDateTime lastNotificationTime,
            LocalDateTime currentTime);

    /**
     * Calculate alert time range based on notification type and settings
     *
     * @param notificationType     Type of notification (ALERT, INFO_PUSH, NO_ALERT)
     * @param settings             Reception settings
     * @param currentTime          Current time
     * @param lastNotificationTime Time of last notification (for NO_ALERT
     *                             calculations)
     * @return Time range result with start and end times
     */
    TimeRangeResult calculateAlertTimeRange(
            NotificationType notificationType,
            ReceptionSettingsDto settings,
            LocalDateTime currentTime,
            LocalDateTime lastNotificationTime);

    /**
     * Calculate the end time of the last valid reception period
     * Used for info push notifications to determine the correct time range
     * Considers reception time (DAILY/WORKDAYS/HOLIDAYS) and reception period
     *
     * @param settings    Reception settings containing reception time and period
     * @param currentTime Current time to calculate from
     * @return End time of the last valid reception period
     */
    LocalDateTime calculateLastReceptionPeriodEnd(ReceptionSettingsDto settings, LocalDateTime currentTime);
}