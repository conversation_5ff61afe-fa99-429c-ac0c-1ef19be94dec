package com.czb.hn.service.job.impl;

import com.czb.hn.dto.bulletin.BulletinParams;
import com.czb.hn.jpa.securadar.entity.BulletinGenerationRecordEntity;
import com.czb.hn.enums.BulletinStatus;
import com.czb.hn.jpa.securadar.repository.BulletinGenerationRecordRepository;
import com.czb.hn.service.bulletin.BulletinContentDataService;
import com.czb.hn.service.bulletin.BulletinGenerator;
import com.czb.hn.service.bulletin.BulletinRecordService;
import com.czb.hn.service.holidays.HolidayService;
import com.czb.hn.service.job.JobInfo;
import com.czb.hn.util.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 简报生成任务
 */
@Component("bulletinGeneratorJob")
public class BulletinGeneratorJob {
    
    private static final Logger log = LoggerFactory.getLogger(BulletinGeneratorJob.class);
    
    private final BulletinGenerationRecordRepository generationRecordRepository;
    
    private final BulletinGenerator bulletinGenerator;
    
    private final HolidayService holidayService;

    private final BulletinContentDataService bulletinContentDataService;
    
    private final BulletinRecordService bulletinRecordService;

    public BulletinGeneratorJob(
            BulletinGenerationRecordRepository generationRecordRepository,
            BulletinGenerator bulletinGenerator,
            HolidayService holidayService, 
            BulletinContentDataService bulletinContentDataService,
            BulletinRecordService bulletinRecordService) {
        this.generationRecordRepository = generationRecordRepository;
        this.bulletinGenerator = bulletinGenerator;
        this.holidayService = holidayService;
        this.bulletinContentDataService = bulletinContentDataService;
        this.bulletinRecordService = bulletinRecordService;
    }

    /**
     * 简报生成任务入口 - 在凌晨执行
     * 配置为 handler="bulletinGeneratorJob#generate"
     */
    public void generate(JobInfo jobInfo) {
        try {
            log.info("开始生成简报数据: {}", jobInfo.getName());
            
            // 解析任务参数
            BulletinParams params = JsonUtil.fromJson(jobInfo.getJobParams(), BulletinParams.class);
            if (params == null) {
                log.error("简报任务参数解析失败: {}", jobInfo.getName());
                return;
            }
            
            // 获取今天日期
            LocalDate today = LocalDate.now();
            
            // 检查今日是否已生成简报
            if (generationRecordRepository.findByJobIdAndBulletinDate(jobInfo.getId(), today).isPresent()) {
                log.info("今日简报已生成，跳过: {}, 日期: {}", jobInfo.getName(), today);
                return;
            }
            
            // 根据简报类型，判断今天是否需要生成简报
            if (!shouldGenerateBulletin(jobInfo, today)) {
                log.info("今日不需要生成简报: {}, 日期: {}", jobInfo.getName(), today);
                return;
            }
            
            // 创建生成记录
            BulletinGenerationRecordEntity generationRecord = createGenerationRecord(jobInfo, params, today);

            try {
                // 计算数据日期范围
                BulletinGenerator.DateRange dateRange = getDataRange(today, params.dataRange(), jobInfo);
                
                // 生成并保存简报内容数据
                String startTimeStr = dateRange.startDate().atStartOfDay().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                String endTimeStr = dateRange.endDate().atTime(23, 59, 59).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                
                // 先生成结构化数据
                bulletinContentDataService.generateAndSaveBulletinContentData(
                    generationRecord.getId(),
                    jobInfo.getId(),
                    startTimeStr,
                    endTimeStr,
                    jobInfo.getJobParams()
                );
                
                // 然后生成PDF内容
                byte[] bulletinContent = bulletinGenerator.generateBulletin(
                    getBulletinType(jobInfo), 
                    today, 
                    dateRange,
                    generationRecord.getId() // 传递生成记录ID
                );
                
                // 保存PDF到MinIO并更新记录
                String filename = params.bulletinTitle() + "_" + today + ".pdf";
                bulletinRecordService.saveBulletinContent(generationRecord.getId(), bulletinContent, filename);
            
                log.info("简报生成成功: {}, 日期: {}", jobInfo.getName(), today);
            } catch (Exception e) {
                log.error("生成简报异常: {}, 日期: {}", jobInfo.getName(), today, e);
                updateGenerationRecordFailure(generationRecord, e.getMessage());
            }
            
        } catch (Exception e) {
            log.error("简报数据生成异常: {}", jobInfo.getName(), e);
        }
    }
    
    /**
     * 创建生成记录实体
     */
    private BulletinGenerationRecordEntity createGenerationRecord(JobInfo jobInfo, BulletinParams params, LocalDate today) {
        String bulletinType = getBulletinType(jobInfo);
        BulletinGenerator.DateRange dateRange = getDataRange(today, params.dataRange(), jobInfo);
        
        // 构建简报标题
        String bulletinTitle = params.bulletinTitle();
        if (bulletinTitle == null || bulletinTitle.isEmpty()) {
            // 默认标题格式：舆情监控简报yyyyMM
            bulletinTitle = "舆情监控简报" + today.format(DateTimeFormatter.ofPattern("yyyyMM"));
        }
        
        // 创建生成记录实体
        BulletinGenerationRecordEntity record = new BulletinGenerationRecordEntity();
        record.setBulletinTitle(bulletinTitle);
        record.setBulletinType(bulletinType);
        record.setStartTime(dateRange.startDate().atStartOfDay());
        record.setEndTime(dateRange.endDate().atTime(23, 59, 59));
        record.setGenerationTime(LocalDateTime.now());
        record.setJobId(jobInfo.getId());
        record.setPlanId(jobInfo.getPlanId()); // 设置方案ID
        record.setBulletinDate(today);
        record.setStatus("PENDING");
        record.setCreatedAt(LocalDateTime.now());
        record.setUpdatedAt(LocalDateTime.now());
        
        // 保存到数据库
        return generationRecordRepository.save(record);
    }
    
    /**
     * 更新生成记录状态为失败
     */
    private void updateGenerationRecordFailure(BulletinGenerationRecordEntity record, String errorMessage) {
        record.setStatus("FAILED");
        record.setUpdatedAt(LocalDateTime.now());
        generationRecordRepository.save(record);
    }
    
    /**
     * 判断指定日期是否需要生成简报
     */
    private boolean shouldGenerateBulletin(JobInfo jobInfo, LocalDate date) {
        String bulletinType = getBulletinType(jobInfo);
        String executionDay = jobInfo.getExecutionDay();
        
        if ("DAILY".equals(bulletinType)) {
            return true; // 日报每天都生成
        } else if ("WEEKLY".equals(bulletinType)) {
            // 判断是否是配置的星期几
            int configDay = Integer.parseInt(executionDay);
            int currentDay = date.getDayOfWeek().getValue(); // 1-7，对应周一到周日
            return configDay == currentDay;
        } else if ("MONTHLY".equals(bulletinType)) {
            // 判断是否是配置的每月几号
            int configDay = Integer.parseInt(executionDay);
            int currentDay = date.getDayOfMonth();
            return configDay == currentDay;
        }
        
        return false;
    }
    
    /**
     * 生成简报内容
     */
//    private byte[] generateBulletinContent(JobInfo jobInfo, BulletinParams params, LocalDate bulletinDate) {
//        try {
//            // 获取简报类型
//            String bulletinType = getBulletinType(jobInfo);
//
//            // 计算数据日期范围
//            BulletinGenerator.DateRange dateRange = getDataRange(bulletinDate, params.dataRange(), jobInfo);
//
//            // 调用简报生成服务
//            return bulletinGenerator.generateBulletin(
//                bulletinType,
//                bulletinDate,
//                dateRange
//            );
//        } catch (Exception e) {
//            log.error("生成简报内容异常: {}, 日期: {}", jobInfo.getName(), bulletinDate, e);
//            throw e;
//        }
//    }
//
    /**
     * 计算数据日期范围
     */
    private BulletinGenerator.DateRange getDataRange(LocalDate bulletinDate, Integer dataRange, JobInfo jobInfo) {
        LocalDate startDate;
        String bulletinType = getBulletinType(jobInfo);
        
        switch (bulletinType) {
            case "DAILY":
                // 日报：前一天的数据
                startDate = bulletinDate.minusDays(1);
                break;
            case "WEEKLY":
                // 周报：前一周的数据（7天）
                startDate = bulletinDate.minusDays(7);
                break;
            case "MONTHLY":
                // 月报：前一个月的数据（根据实际月份天数计算）
                startDate = bulletinDate.minusMonths(1);
                break;
            default:
                // 默认使用参数中的dataRange，如果有的话
                if (dataRange != null && dataRange > 0) {
                    startDate = bulletinDate.minusDays(dataRange - 1);
                } else {
                    startDate = bulletinDate.minusDays(1); // 默认前一天
                }
        }
        
        return new BulletinGenerator.DateRange(startDate, bulletinDate);
    }
    
    /**
     * 获取简报类型
     */
    private String getBulletinType(JobInfo jobInfo) {
        // 从任务名称中提取简报类型
        String jobName = jobInfo.getName().toUpperCase();
        if (jobName.contains("DAILY")) {
            return "DAILY";
        } else if (jobName.contains("WEEKLY")) {
            return "WEEKLY";
        } else if (jobName.contains("MONTHLY")) {
            return "MONTHLY";
        } else {
        // 默认为日报
        return "DAILY";
        }
    }
} 